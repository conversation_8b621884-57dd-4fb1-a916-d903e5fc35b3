using System.Security.Cryptography;
using System.Text;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using IotPlatform.DongleLicense.Models;
using Microsoft.AspNetCore.Mvc;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
///     加密锁许可证管理服务
/// </summary>
[ApiDescriptionSettings("加密锁许可证")]
public class DongleLicenseService : IDynamicApiController, ITransient
{
    private readonly DongleCheckService _checkService;
    private readonly DongleTriggerService _triggerService;
    private readonly DongleAuthService _authService;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="checkService">检查服务</param>
    /// <param name="triggerService">触发服务</param>
    /// <param name="authService">授权服务</param>
    public DongleLicenseService(
        DongleCheckService checkService,
        DongleTriggerService triggerService,
        DongleAuthService authService)
    {
        _checkService = checkService;
        _triggerService = triggerService;
        _authService = authService;
    }

    /// <summary>
    ///     手动触发检查
    /// </summary>
    /// <returns>检查结果</returns>
    [HttpPost("/dongleLicense/triggerCheck")]
    public async Task<DongleCheckResult> TriggerCheck()
    {
        return await _triggerService.TriggerCheckAsync();
    }

    /// <summary>
    ///     获取系统健康状态（包含加密锁状态）
    /// </summary>
    /// <returns>健康状态：true表示授权验证通过，false表示验证失败</returns>
    [HttpGet("/dongleLicense/health")]
    public async Task<bool> GetHealth()
    {
        try
        {
            // 1. 读取授权密钥文件内容
            string? storedAuthKey = _authService.GetStoredAuthKey();
            if (string.IsNullOrEmpty(storedAuthKey))
            {
                return false; // 未找到授权密钥文件
            }

            // 2. 获取加密狗硬件ID
            DongleCheckResult checkResult = await _checkService.CheckDongleAsync();
            if (!checkResult.IsSuccess || checkResult.DongleInfo == null)
            {
                return false; // 无法获取加密狗信息
            }

            // 获取设备的硬件ID
            if (string.IsNullOrEmpty(checkResult.DongleInfo.HardwareId))
            {
                return false; // 无法获取硬件ID
            }

            // 3. 对硬件ID进行MD5加密并对比
            string currentAuthKey = GenerateMD5Hash(checkResult.DongleInfo.HardwareId);

            // 4. 对比授权密钥，一致返回true，否则返回false
            return string.Equals(storedAuthKey, currentAuthKey, StringComparison.OrdinalIgnoreCase);
        }
        catch (Exception)
        {
            // 发生任何异常都返回false
            return false;
        }
    }

    /// <summary>
    ///     生成MD5哈希值
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>MD5哈希值</returns>
    private static string GenerateMD5Hash(string input)
    {
        using MD5 md5 = MD5.Create();
        byte[] inputBytes = Encoding.UTF8.GetBytes(input);
        byte[] hashBytes = md5.ComputeHash(inputBytes);
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    /// <summary>
    ///     初始化授权密钥（一次性操作）
    /// </summary>
    /// <param name="input">初始化授权输入参数</param>
    /// <returns>初始化结果</returns>
    [HttpPost("/dongleLicense/initializeAuth")]
    public async Task<dynamic> InitializeAuthorization([FromBody] InitializeAuthInput input)
    {
        try
        {
            // 参数验证
            if (input == null || string.IsNullOrWhiteSpace(input.HardwareId))
            {
                return new
                {
                    success = false,
                    message = "硬件ID不能为空",
                    timestamp = DateTime.Now
                };
            }

            // 检查是否已经初始化过
            if (_authService.IsAuthInitialized())
            {
                return new
                {
                    success = false,
                    message = "授权密钥已存在，无法重复初始化",
                    timestamp = DateTime.Now
                };
            }

            // 保存用户传入的授权密钥到文件
            bool saveResult = _authService.SaveAuthKey(input.HardwareId.Trim());
            if (!saveResult)
            {
                return new
                {
                    success = false,
                    message = "保存授权密钥失败",
                    timestamp = DateTime.Now
                };
            }

            return new
            {
                success = true,
                message = "授权密钥初始化成功",
                hardwareId = input.HardwareId.Trim(),
                timestamp = DateTime.Now
            };
        }
        catch (Exception ex)
        {
            return new
            {
                success = false,
                message = $"初始化授权密钥时发生异常: {ex.Message}",
                timestamp = DateTime.Now
            };
        }
    }

    /// <summary>
    ///     检查是否已初始化授权密钥
    /// </summary>
    /// <returns>授权初始化状态</returns>
    [HttpGet("/dongleLicense/hasAuthKey")]
    public dynamic HasAuthorizationKey()
    {
        bool isInitialized = _authService.IsAuthInitialized();

        return new
        {
            hasAuthKey = isInitialized,
            isInitialized,
            message = isInitialized ? "已初始化授权密钥" : "未初始化授权密钥",
            timestamp = DateTime.Now
        };
    }
}